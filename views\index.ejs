<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Football Club Home</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="/stylesheet/style.css" />
</head>
<body>
  <!-- Navbar -->
  <%- include('partials/navbar') %>

  <main class="container mt-4">
    <!-- Blue Lock Club Section (Slideshow placeholder) -->
    <section class="bluelock-section mb-5">
      <h1 class="text-center mb-4">Blue Lock Club</h1>
      <div class="slideshow-container">
        <div class="slideshow-placeholder">
          <p class="text-center text-muted">Slideshow of character pics</p>
          <!-- Slideshow dots indicator -->
          <div class="slideshow-dots text-center mt-3">
            <span class="dot active"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    </section>

    <!-- Shop Section -->
    <section class="shop-section mb-5">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>SHOP NOW</h2>
        <a href="/store" class="btn btn-outline-light">View Store</a>
      </div>

      <!-- Shop items with scroll arrows -->
      <div class="shop-scroll-wrapper position-relative">
        <!-- Shop items container -->
        <div class="shop-scroll-container" id="shopContainer">
          <div class="shop-scroll-items">

            <!-- Shop Item 1 -->
            <div class="shop-item-card">
              <div class="shop-item-image-container">
                <div class="shop-item-image">
                  <img src="#" alt="football">
                </div>
              </div>
              <div class="shop-item-details">
                <h6 class="shop-item-title">Football</h6>
                <p class="shop-item-price">£146.00</p>
                <button class="btn-buy-now">BUY NOW</button>
              </div>
            </div>

            <!-- Shop Item 2 -->
            <div class="shop-item-card">
              <div class="shop-item-image-container">
                <div class="shop-item-image">
                  <img src="#" alt="jersey">
                </div>
              </div>
              <div class="shop-item-details">
                <h6 class="shop-item-title">Jersey</h6>
                <p class="shop-item-price">£111.00</p>
                <button class="btn-buy-now">BUY NOW</button>
              </div>
            </div>

            <!-- Shop Item 3 -->
            <div class="shop-item-card">
              <div class="shop-item-image-container">
                <div class="shop-item-image">
                  <img src="#" alt="boots">
                </div>
              </div>
              <div class="shop-item-details">
                <h6 class="shop-item-title">Boots</h6>
                <p class="shop-item-price">£111.00</p>
                <button class="btn-buy-now">BUY NOW</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Arrow -->
        <button class="shop-arrow shop-arrow-right" onclick="scrollShopRight()">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </section>

    <!-- Upcoming Matches Section -->
    <section class="matches-section mb-5">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>UPCOMING MATCHES</h2>
        <div class="countdown">
          <span id="days">00</span> DAYS
          <span id="hours">00</span> HOURS
          <span id="mins">00</span> MINS
          <span id="secs">00</span> SECS
        </div>
        <a href="#" class="btn btn-outline-light btn-sm">
          <i class="fas fa-sync"></i> Sync to calendar
        </a>
      </div>

      <div class="match-scroll-container">
        <div class="match-scroll-wrapper">
          <!-- Previous Games -->
          <div class="match-card previous-game">
            <h4>PREVIOUS GAMES</h4>
            <a href="/matches" class="text-white">See all the results ></a>
          </div>

          <!-- Match 1 -->
          <div class="match-card">
            <div class="d-flex justify-content-between mb-2">
              <div class="score">3</div>
              <div><i class="fas fa-trophy text-warning"></i></div>
              <div class="score">2</div>
            </div>
            <h5>Barcelona vs Real Madrid</h5>
            <p class="mb-1">Sunday, April 27, 14:00 CET</p>
            <p class="mb-1">Copa Del Rey, Estadio Olímpico de la Cartuja</p>
            <a href="#" class="btn btn-sm btn-outline-primary">Match Center</a>
          </div>

          <!-- Match 2 -->
          <div class="match-card">
            <div class="d-flex justify-content-between mb-2">
              <div class="score">3</div>
              <div><i class="fas fa-trophy text-warning"></i></div>
              <div class="score">3</div>
            </div>
            <h5>Girona vs Mallorca</h5>
            <p class="mb-1">Thursday, May 1, 3:00 CET</p>
            <p class="mb-1">UEFA Champions League, Estadi Olímpic Lluís Companys</p>
            <a href="#" class="btn btn-sm btn-outline-primary">Match Center</a>
          </div>

          <!-- Next Game -->
          <div class="match-card next-game">
            <h4>NEXT GAME</h4>
            <a href="/matches" class="text-white">See all the results ></a>
          </div>
        </div>
      </div>
    </section>

    <!-- Trending Section -->
    <section class="trending-section mb-5">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>TRENDING</h2>
      </div>
      <div class="trending-scroll-container">
        <div class="trending-scroll-wrapper">
          <div class="trending-item">
            <span class="trending-badge badge-live">LIVE</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Watch U18s Football</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-live">LIVE</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Friendly Games U20</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Rising Star Players</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Exclusive Clips</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Youngest to 100 games</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>HIGHLIGHTS | Final</h6>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h6>Matchday Challenge</h6>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="news-section mb-5">
      <div class="news-header mb-4">
        <h2 class="news-title">NEWS</h2>
        <a href="/news" class="btn btn-outline-light">View All News</a>
      </div>

      <!-- Main News Article -->
      <div class="main-news-card mb-4">
        <div class="main-news-content">
          <!-- Content left blank as requested -->
        </div>
      </div>

      <!-- Secondary News Items -->
      <div class="secondary-news-grid">
        <div class="secondary-news-item">
          <div class="secondary-news-content">
            <!-- Content left blank as requested -->
          </div>
        </div>

        <div class="secondary-news-item">
          <div class="secondary-news-content">
            <!-- Content left blank as requested -->
          </div>
        </div>

        <div class="secondary-news-item">
          <div class="secondary-news-content">
            <!-- Content left blank as requested -->
          </div>
        </div>

        <div class="secondary-news-item">
          <div class="secondary-news-content">
            <!-- Content left blank as requested -->
          </div>
        </div>
      </div>
    </section>
  </main>

<!-- JavaScript functions -->
  <!-- Countdown Timer Script -->
  <script>
    // Set the date to count down to (next match date)
    const countDownDate = new Date("May 1, 2025 15:00:00").getTime();

    // Update the countdown every 1 second
    const x = setInterval(function() {
      // Get today's date and time
      const now = new Date().getTime();

      // Find the distance between now and the count down date
      const distance = countDownDate - now;

      // Time calculations for days, hours, minutes and seconds
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      // Display the result
      document.getElementById("days").innerHTML = days.toString().padStart(2, '0');
      document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
      document.getElementById("mins").innerHTML = minutes.toString().padStart(2, '0');
      document.getElementById("secs").innerHTML = seconds.toString().padStart(2, '0');

      // If the count down is finished, write some text
      if (distance < 0) {
        clearInterval(x);
        document.getElementById("countdown").innerHTML = "MATCH DAY!";
      }
    }, 1000);

    // Shop section - scroll functionality
    function scrollShopLeft() {
      const container = document.getElementById('shopContainer');
      container.scrollBy({
        left: -300, // Scroll left by 300px
        behavior: 'smooth'
      });
    }

    function scrollShopRight() {
      const container = document.getElementById('shopContainer');
      container.scrollBy({
        left: 300, // Scroll right by 300px
        behavior: 'smooth'
      });
    }
  </script>

  <%- include('partials/footer') %>
</body>
</html>