<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shopping Cart</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/stylesheet/style.css" />
</head>
<body>
  <!-- Navbar -->
  <%- include('partials/navbar') %>

  <main class="container mt-5">
    <h1 class="text-center">Shopping Cart</h1>
    <% if (cart && cart.length > 0) { %>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Item</th>
            <th>Description</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <% let totalAmount = 0; %>
          <% let totalItems = 0; %>
          <% cart.forEach(item => { %>
            <tr>
              <td><%= item.gear_name %></td>
              <td><%= item.gear_desc %></td>
              <td>$<%= item.price_per_unit %></td>
              <td>
                <form action="/cart/update/<%= item.gear_id %>" method="POST" style="display: inline;">
                  <input type="number" name="quantity" value="<%= item.quantity || 1 %>" min="1" class="form-control form-control-sm d-inline-block" style="width: 60px;">
                  <button type="submit" class="btn btn-primary btn-sm">Update</button>
                </form>
              </td>
              <td>
                <form action="/cart/remove/<%= item.gear_id %>" method="POST" style="display: inline;">
                  <button type="submit" class="btn btn-danger btn-sm">Remove</button>
                </form>
              </td>
            </tr>
            <% totalAmount += parseFloat(item.price_per_unit) * (item.quantity || 1); %>
            <% totalItems += (item.quantity || 1); %>
          <% }) %>
        </tbody>
      </table>
      <div class="text-end">
        <h4>Total Items: <%= totalItems %></h4>
        <h4>Total Price: $<%= totalAmount.toFixed(2) %></h4>
      </div>
      <div class="text-end mt-3">
        <a href="/store" class="btn btn-secondary">Add More to Cart</a>
        <a href="/payment" class="btn btn-success">Proceed to Payment</a>
      </div>
    <% } else { %>
      <p class="text-center">Your cart is empty.</p>
      <div class="text-center mt-3">
        <a href="/store" class="btn btn-primary">Go to Store</a>
      </div>
    <% } %>

    <!-- Cashback Placeholder -->
    <% if (!isMember) { %>
      <div class="alert alert-info mt-4 text-center">
        <p>Register as a member today to earn cashback!</p>
        <a href="/register" class="btn btn-warning">Register Now</a>
      </div>
    <% } else { %>
      <div class="alert alert-success mt-4 text-center">
        <p>Cashback Accumulation Placeholder: Earn cashback on your purchases!</p>
      </div>
    <% } %>
  </main>
</body>
</html>