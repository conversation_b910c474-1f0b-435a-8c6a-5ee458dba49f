<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/stylesheet/style.css" />
</head>
<body>
  <%- include('partials/navbar') %>

  <main class="container mt-5">
    <h1 class="text-center mb-4">Admin Dashboard</h1>

    <div class="row align-items-center mb-4">
      <!-- Profile Picture -->
      <div class="col-md-3 text-center">
        <img src="/images/admin-profile.png" alt="Admin Profile" class="img-fluid rounded-circle" style="width: 180px;">
      </div>

      <!-- Name and Role -->
      <div class="col-md-9">
        <h2><%= admin.username %></h2>
        <h5 class="text-muted">Role: Admin</h5>
      </div>
    </div>

    <!-- Admin Info -->
    <div class="row mb-5">
      <div class="col-md-6">
        <p><strong>Email:</strong> <%= admin.email %></p>
        <p><strong>Phone:</strong> <%= admin.phone %></p>
      </div>
      <div class="col-md-6">
        <p><strong>Admin Since:</strong> <%= admin.joinDate %></p>
      </div>
    </div>

    <!-- Admin Actions -->
    <h3 class="mb-3">Admin Controls</h3>
    <div class="row g-3 mb-5">
      <div class="col-md-3">
        <a href="/admin/transactions" class="btn btn-dark w-100">View Transactions</a>
      </div>
      <div class="col-md-3">
        <a href="/admin/stocks" class="btn btn-dark w-100">Manage Stock</a>
      </div>
      <div class="col-md-3">
        <a href="/admin/items" class="btn btn-dark w-100">Add/Delete Items</a>
      </div>
      <div class="col-md-3">
        <a href="/admin/players" class="btn btn-dark w-100">Add/Delete Players</a>
      </div>
    </div>

    <!-- Dummy Table Example (e.g. recent transactions) -->
    <h4 class="mt-5">Recent Transactions</h4>
    <table class="table table-striped mt-3">
      <thead>
        <tr>
          <th>#</th>
          <th>User</th>
          <th>Item</th>
          <th>Date</th>
          <th>Total</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>1</td>
          <td>user123</td>
          <td>Club Jersey</td>
          <td>2025-04-28</td>
          <td>$49.99</td>
        </tr>
        <tr>
          <td>2</td>
          <td>fan99</td>
          <td>Match Ticket</td>
          <td>2025-04-25</td>
          <td>$15.00</td>
        </tr>
      </tbody>
    </table>
  </main>

  <%- include('partials/footer') %>
</body>
</html>