
/*footer*/
html, body {
    height: 100%;
  }

  body {
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }



/* Upcoming matches section */
.matches-section {
  background-color: #1a237e;
  color: #ffd700;
  padding: 40px 20px;
  border-radius: 0;
  margin-bottom: 40px;
}

.matches-section h2 {
  color: #ffd700;
  font-size: 2.5rem;
  font-weight: bold;
}

.countdown {
  color: white;
  font-weight: bold;
}

/* Scrollable match container */
.match-scroll-container {
  overflow-x: auto;
  padding-bottom: 10px;
  scroll-behavior: smooth; /* Modern smooth scrolling */
}

/* Custom scrollbar styling for webkit browsers */
.match-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.match-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.match-scroll-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.match-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.match-scroll-wrapper {
  display: flex;
  gap: 15px;
  padding: 5px 0;
  min-width: min-content;
}

.match-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  min-width: 280px;
  flex: 0 0 auto;
  color: #333;
}

.match-card .score {
  font-size: 1.5rem;
  font-weight: bold;
}

.match-card.next-game, .match-card.previous-game {
  background: linear-gradient(135deg, #d32f2f 0%, #9c27b0 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.match-card.next-game h4, .match-card.previous-game h4 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.match-card.next-game a, .match-card.previous-game a {
  text-decoration: none;
  font-weight: bold;
}

.match-card.next-game a:hover, .match-card.previous-game a:hover {
  text-decoration: underline;
}

/* Trending section */
.trending-section {
  background-color: #1a237e;
  padding: 40px 20px;
  margin-bottom: 40px;
  border-radius: 0;
}

.trending-section h2 {
  color: #ffd700;
  font-size: 2.5rem;
  font-weight: bold;
}

/* Scrollable trending container */
.trending-scroll-container {
  overflow-x: auto;
  padding-bottom: 10px;
  scroll-behavior: smooth; /* Modern smooth scrolling */
  direction: rtl; /* Make it scroll from right to left */
}

/* Custom scrollbar styling for webkit browsers */
.trending-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.trending-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.trending-scroll-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.trending-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.trending-scroll-wrapper {
  display: flex;
  gap: 15px;
  padding: 5px 0;
  min-width: min-content;
  direction: ltr; /* Reset the text direction */
}

.trending-item {
  background-color: #1a237e;
  border-radius: 8px;
  padding: 10px;
  height: 120px;
  position: relative;
  color: white;
  min-width: 200px;
  flex: 0 0 auto;
}

.trending-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.badge-live {
  background-color: #f44336;
}

.badge-new {
  background-color: #ffd700;
  color: #1a237e;
}

/* News section */
.news-section {
  background-color: #1a237e;
  padding: 40px 20px;
  margin-bottom: 40px;
  border-radius: 0;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.news-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffd700;
  margin: 0;
}

/* Main news card */
.main-news-card {
  background-color: #888;
  border-radius: 15px;
  height: 300px;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.main-news-content {
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding: 30px;
}

/* Secondary news grid */
.secondary-news-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.secondary-news-item {
  background-color: #000;
  border-radius: 15px;
  height: 120px;
  position: relative;
  overflow: hidden;
}

.secondary-news-content {
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding: 15px;
}

@media (max-width: 768px) {
  .secondary-news-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .secondary-news-grid {
    grid-template-columns: 1fr;
  }
}

/* Membership cards */
.membership-card {
  padding: 20px;
  text-align: center;
  border-radius: 10px;
}

.gold {
  background-color: #ffd700;
  color: #3425a5;
}

.silver {
  background-color: #c0c0c0;
  color: #3425a5;
}

.bronze {
  background-color: #cd7f32;
  color: #3425a5;
}

/* Adjust the logo size */
.navbar .logo {
    height: 50px; /* Set a fixed height */
    width: auto; /* Maintain aspect ratio */
    max-width: 150px; /* Optional: Limit the maximum width */
    object-fit: contain; /* Ensure the image fits within the specified dimensions */
    margin-right: 10px; /* Add spacing if needed */
}

/* Ensure the navbar background color is consistent */
.navbar {
    background-color: #212529 !important; /* Dark background */
}

/* Players EJS */
.player-card {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.player-card:hover {
  transform: scale(1.05);
}

.player-info {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  color: #fff;
  width: 100%;
  padding: 8px;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  font-weight: bold;
}

.player-card:hover .player-info {
  transform: translateY(0);
}

.player-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

h2 {
  margin-top: 40px;
  margin-bottom: 20px;
}

/* Blue Lock Club Section */
.bluelock-section {
  margin-bottom: 40px;
}

.slideshow-container {
  background-color: #f8f9fa;
  border-radius: 15px;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slideshow-placeholder {
  color: #6c757d;
  font-size: 1.2rem;
}

/* Slideshow dots */
.slideshow-dots {
  margin-top: 20px;
}

.dot {
  height: 12px;
  width: 12px;
  margin: 0 5px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot.active, .dot:hover {
  background-color: #717171;
}

/* Shop Section */
.shop-section {
  background-color: #1a237e;
  padding: 40px 20px;
  margin-bottom: 40px;
  border-radius: 0;
}

.shop-header {
  text-align: left;
}

.shop-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffd700;
  margin: 0;
  letter-spacing: 1px;
}

.shop-scroll-wrapper {
  position: relative;
}

.shop-scroll-container {
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 10px 50px 10px 10px; /* Add padding for right arrow */
}

.shop-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.shop-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.shop-scroll-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.shop-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.shop-scroll-items {
  display: flex;
  gap: 20px;
  padding: 10px 0;
  min-width: min-content;
}

.shop-item-card {
  background-color: white;
  border-radius: 8px;
  min-width: 280px;
  flex: 0 0 auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  overflow: hidden;
}

.shop-item-card:hover {
  transform: translateY(-5px);
}

.shop-item-image-container {
  background-color: #e8e8e8;
  padding: 20px;
  text-align: center;
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shop-item-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.shop-item-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.shop-item-details {
  padding: 20px;
  text-align: left;
}

.shop-item-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.shop-item-price {
  font-size: 1.25rem;
  font-weight: bold;
  color: #000;
  margin-bottom: 15px;
}

.btn-buy-now {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 12px 0;
  width: 100%;
  border-radius: 25px;
  font-weight: bold;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-buy-now:hover {
  background-color: #b71c1c;
}

/* Shop scroll arrows */
.shop-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shop-arrow:hover {
  background-color: #f8f9fa;
  border-color: #333;
  color: #333;
}

.shop-arrow-right {
  right: 10px;
}
